import { MongoClient, ObjectId } from 'mongodb';
import { error, fail } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const priceList = db.collection('PriceLIst');

    // Fetch all price list items
    const items = await priceList.find({}).sort({ category: 1, partNumber: 1 }).toArray();

    // Get unique categories for filtering
    const categories = await priceList.distinct('category');

    // Get unique suppliers for filtering
    const suppliers = await priceList.distinct('supplier');

    return {
      items: items.map(item => ({
        ...item,
        _id: item._id.toString()
      })),
      categories: categories.filter(Boolean).sort(),
      suppliers: suppliers.filter(Boolean).sort()
    };

  } catch (err) {
    console.error('Error loading price list:', err);
    throw error(500, 'Failed to load price list');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  create: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();

      const itemData = {
        partNumber: formData.get('partNumber'),
        description: formData.get('description'),
        unitPrice: parseFloat(formData.get('unitPrice')) || 0,
        currency: formData.get('currency') || 'EUR',
        category: formData.get('category'),
        supplier: formData.get('supplier'),
        discountCategory: formData.get('discountCategory'),
        isActive: formData.get('isActive') === 'on',
        validFrom: formData.get('validFrom') ? new Date(formData.get('validFrom')) : null,
        validTo: formData.get('validTo') ? new Date(formData.get('validTo')) : null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Validate required fields
      if (!itemData.partNumber || !itemData.description) {
        return fail(400, { error: 'Part number and description are required' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.insertOne(itemData);

      if (!result.acknowledged) {
        return fail(500, { error: 'Failed to create price list item' });
      }

      return { success: true, id: result.insertedId.toString() };

    } catch (err) {
      console.error('Error creating price list item:', err);
      return fail(500, { error: 'Failed to create price list item' });
    } finally {
      await client.close();
    }
  },

  update: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();
      const id = formData.get('_id');

      if (!ObjectId.isValid(id)) {
        return fail(400, { error: 'Invalid item ID' });
      }

      const updateData = {
        partNumber: formData.get('partNumber'),
        description: formData.get('description'),
        unitPrice: parseFloat(formData.get('unitPrice')) || 0,
        currency: formData.get('currency') || 'EUR',
        category: formData.get('category'),
        supplier: formData.get('supplier'),
        discountCategory: formData.get('discountCategory'),
        isActive: formData.get('isActive') === 'on',
        validFrom: formData.get('validFrom') ? new Date(formData.get('validFrom')) : null,
        validTo: formData.get('validTo') ? new Date(formData.get('validTo')) : null,
        updatedAt: new Date()
      };

      // Validate required fields
      if (!updateData.partNumber || !updateData.description) {
        return fail(400, { error: 'Part number and description are required' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.updateOne(
        { _id: new ObjectId(id) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true };

    } catch (err) {
      console.error('Error updating price list item:', err);
      return fail(500, { error: 'Failed to update price list item' });
    } finally {
      await client.close();
    }
  },

  delete: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();
      const id = formData.get('_id');

      if (!ObjectId.isValid(id)) {
        return fail(400, { error: 'Invalid item ID' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.deleteOne({ _id: new ObjectId(id) });

      if (result.deletedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true };

    } catch (err) {
      console.error('Error deleting price list item:', err);
      return fail(500, { error: 'Failed to delete price list item' });
    } finally {
      await client.close();
    }
  }
};
