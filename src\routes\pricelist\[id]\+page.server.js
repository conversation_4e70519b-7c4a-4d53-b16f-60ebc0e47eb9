import { MongoClient, ObjectId } from 'mongodb';
import { error, fail } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  const client = new MongoClient(uri);

  try {
    if (!ObjectId.isValid(params.id)) {
      throw error(400, 'Invalid item ID format');
    }

    await client.connect();
    const db = client.db('ServiceContracts');
    const priceList = db.collection('PriceLIst');

    // Find the specific price list item
    const item = await priceList.findOne({ _id: new ObjectId(params.id) });

    if (!item) {
      throw error(404, 'Price list item not found');
    }

    return {
      item: {
        ...item,
        _id: item._id.toString()
      }
    };

  } catch (err) {
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    console.error('Error loading price list item:', err);
    throw error(500, 'Failed to load price list item');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  update: async ({ request, params }) => {
    const client = new MongoClient(uri);

    try {
      if (!ObjectId.isValid(params.id)) {
        return fail(400, { error: 'Invalid item ID format' });
      }

      const formData = await request.formData();

      const updateData = {
        partNumber: formData.get('partNumber'),
        description: formData.get('description'),
        unitPrice: parseFloat(formData.get('unitPrice')) || 0,
        currency: formData.get('currency') || 'EUR',
        category: formData.get('category'),
        supplier: formData.get('supplier'),
        discountCategory: formData.get('discountCategory'),
        isActive: formData.get('isActive') === 'on',
        validFrom: formData.get('validFrom') ? new Date(formData.get('validFrom')) : null,
        validTo: formData.get('validTo') ? new Date(formData.get('validTo')) : null,
        updatedAt: new Date()
      };

      // Validate required fields
      if (!updateData.partNumber || !updateData.description) {
        return fail(400, { error: 'Part number and description are required' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.updateOne(
        { _id: new ObjectId(params.id) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true };

    } catch (err) {
      console.error('Error updating price list item:', err);
      return fail(500, { error: 'Failed to update price list item' });
    } finally {
      await client.close();
    }
  },

  delete: async ({ params }) => {
    const client = new MongoClient(uri);

    try {
      if (!ObjectId.isValid(params.id)) {
        return fail(400, { error: 'Invalid item ID format' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.deleteOne({ _id: new ObjectId(params.id) });

      if (result.deletedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true, deleted: true };

    } catch (err) {
      console.error('Error deleting price list item:', err);
      return fail(500, { error: 'Failed to delete price list item' });
    } finally {
      await client.close();
    }
  }
};
