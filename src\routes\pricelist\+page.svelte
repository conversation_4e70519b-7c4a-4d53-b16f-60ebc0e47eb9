<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';

  export let data: import('./$types').PageData;

  let items = (data.items || []) as any[];
  let productGroups = data.productGroups || [];
  let functionGroups = data.functionGroups || [];
  let countries = data.countries || [];

  // Filter and search state
  let searchTerm = '';
  let productGroupFilter = '';
  let functionGroupFilter = '';
  let countryFilter = '';
  let sortField = 'Part No';
  let sortDirection = 'asc';

  // Pagination
  let currentPage = 1;
  let itemsPerPage = 20;

  // Form state
  let showForm = false;
  let editingItem: any = null;
  let formData: any = {
    partNo: '',
    checkDigit: 0,
    description: '',
    priceExclVAT: '',
    discountCode: 0,
    productGroup: 0,
    functionGroup: 0,
    statisticNo: 0,
    weight: '',
    countryOfOrigin: ''
  };

  // Computed filtered and sorted items
  $: filteredItems = items.filter(item => {
    const matchesSearch = !searchTerm ||
      item["Part No"]?.toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
      item["Description"]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item["Country of Origin"]?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesProductGroup = !productGroupFilter || item["Product group"] === parseInt(productGroupFilter);
    const matchesFunctionGroup = !functionGroupFilter || item["Function group"] === parseInt(functionGroupFilter);
    const matchesCountry = !countryFilter || item["Country of Origin"] === countryFilter;

    return matchesSearch && matchesProductGroup && matchesFunctionGroup && matchesCountry;
  });

  $: sortedItems = [...filteredItems].sort((a, b) => {
    const aVal = a[sortField] || '';
    const bVal = b[sortField] || '';

    if (sortDirection === 'asc') {
      return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
    } else {
      return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
    }
  });

  $: totalPages = Math.ceil(sortedItems.length / itemsPerPage);
  $: paginatedItems = sortedItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  function resetForm() {
    editingItem = null;
    formData = {
      partNo: '',
      checkDigit: 0,
      description: '',
      priceExclVAT: '',
      discountCode: 0,
      productGroup: 0,
      functionGroup: 0,
      statisticNo: 0,
      weight: '',
      countryOfOrigin: ''
    };
    showForm = false;
  }

  function editItem(item: any) {
    editingItem = item;
    formData = {
      partNo: item["Part No"] || '',
      checkDigit: item["Check digit"] || 0,
      description: item["Description"] || '',
      priceExclVAT: item["Price excl VAT"] || '',
      discountCode: item["Discount Code"] || 0,
      productGroup: item["Product group"] || 0,
      functionGroup: item["Function group"] || 0,
      statisticNo: item["Statistic No"] || 0,
      weight: item[" Weight "] || '',
      countryOfOrigin: item["Country of Origin"] || ''
    };
    showForm = true;
  }

  function deleteItem(item: any) {
    if (confirm(`Are you sure you want to delete "${item["Part No"]}"?`)) {
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '?/delete';

      const idInput = document.createElement('input');
      idInput.type = 'hidden';
      idInput.name = '_id';
      idInput.value = item._id;
      form.appendChild(idInput);

      document.body.appendChild(form);
      form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(form);
        const response = await fetch('?/delete', {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          window.location.reload();
        }

        document.body.removeChild(form);
      });

      form.requestSubmit();
    }
  }

  function selectItem(item: any) {
    goto(`/pricelist/${item._id}`);
  }

  function handleSort(field: string) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function formatCurrency(amount: number, currency = 'EUR') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount || 0);
  }

  function formatDate(dateString: string) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  }
</script>

<div class="container">
  <div class="header">
    <div class="header-left">
      <button class="btn-secondary back-button" on:click={() => goto('/')}>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
        Back to Landing Page
      </button>
      <h1>Price List Management</h1>
    </div>
    <button class="btn-primary" on:click={() => { showForm = true; editingItem = null; }}>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      Add New Item
    </button>
  </div>

  <!-- Filters -->
  <div class="filters">
    <input
      type="text"
      placeholder="Search by part number, description, or supplier..."
      bind:value={searchTerm}
      class="search-input"
    />

    <select bind:value={productGroupFilter} class="filter-select">
      <option value="">All Product Groups</option>
      {#each productGroups as group}
        <option value={group}>{group}</option>
      {/each}
    </select>

    <select bind:value={functionGroupFilter} class="filter-select">
      <option value="">All Function Groups</option>
      {#each functionGroups as group}
        <option value={group}>{group}</option>
      {/each}
    </select>

    <select bind:value={countryFilter} class="filter-select">
      <option value="">All Countries</option>
      {#each countries as country}
        <option value={country}>{country}</option>
      {/each}
    </select>
  </div>

  <!-- Results info -->
  <div class="results-info">
    Showing {paginatedItems.length} of {sortedItems.length} items
    {#if sortedItems.length !== items.length}
      (filtered from {items.length} total)
    {/if}
  </div>

  <!-- Price List Table -->
  <div class="table-container">
    <table class="price-table">
      <thead>
        <tr>
          <th class="sortable" on:click={() => handleSort('Part No')}>
            Part No
            {#if sortField === 'Part No'}
              <span class="sort-indicator">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            {/if}
          </th>
          <th class="sortable" on:click={() => handleSort('Description')}>
            Description
            {#if sortField === 'Description'}
              <span class="sort-indicator">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            {/if}
          </th>
          <th class="sortable" on:click={() => handleSort('Price excl VAT')}>
            Price excl VAT
            {#if sortField === 'Price excl VAT'}
              <span class="sort-indicator">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            {/if}
          </th>
          <th class="sortable" on:click={() => handleSort('Product group')}>
            Product Group
            {#if sortField === 'Product group'}
              <span class="sort-indicator">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            {/if}
          </th>
          <th class="sortable" on:click={() => handleSort('Country of Origin')}>
            Country
            {#if sortField === 'Country of Origin'}
              <span class="sort-indicator">{sortDirection === 'asc' ? '↑' : '↓'}</span>
            {/if}
          </th>
          <th>Weight</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {#each paginatedItems as item (item._id)}
          <tr class="table-row">
            <td>{item["Part No"] || 'N/A'}</td>
            <td class="description-cell">{item["Description"] || 'N/A'}</td>
            <td>{item["Price excl VAT"] || 'N/A'}</td>
            <td>{item["Product group"] || 'N/A'}</td>
            <td>{item["Country of Origin"] || 'N/A'}</td>
            <td>{item[" Weight "] || 'N/A'}</td>
            <td class="actions-cell">
              <button class="btn-icon view" on:click={() => selectItem(item)} title="View Details" aria-label="View Details">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <button class="btn-icon edit" on:click={() => editItem(item)} title="Edit" aria-label="Edit">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
              <button class="btn-icon delete" on:click={() => deleteItem(item)} title="Delete" aria-label="Delete">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="pagination">
      <button
        class="pagination-btn"
        disabled={currentPage === 1}
        on:click={() => currentPage = Math.max(1, currentPage - 1)}
      >
        Previous
      </button>

      {#each Array(totalPages) as _, i}
        <button
          class="pagination-btn {currentPage === i + 1 ? 'active' : ''}"
          on:click={() => currentPage = i + 1}
        >
          {i + 1}
        </button>
      {/each}

      <button
        class="pagination-btn"
        disabled={currentPage === totalPages}
        on:click={() => currentPage = Math.min(totalPages, currentPage + 1)}
      >
        Next
      </button>
    </div>
  {/if}

  <!-- Form Modal -->
  {#if showForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{editingItem ? 'Edit Price List Item' : 'Add New Price List Item'}</h2>
          <button class="btn-icon" on:click={resetForm} aria-label="Close modal">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form
          action={editingItem ? '?/update' : '?/create'}
          method="POST"
          use:enhance={() => {
            return async ({ result }) => {
              if (result.type === 'success') {
                resetForm();
                window.location.reload();
              }
            };
          }}
        >
          {#if editingItem}
            <input type="hidden" name="_id" value={editingItem._id} />
          {/if}

          <div class="form-grid">
            <div class="form-group">
              <label for="partNo">Part Number*</label>
              <input
                type="number"
                id="partNo"
                name="partNo"
                required
                bind:value={formData.partNo}
              />
            </div>

            <div class="form-group">
              <label for="checkDigit">Check Digit</label>
              <input
                type="number"
                id="checkDigit"
                name="checkDigit"
                min="0"
                bind:value={formData.checkDigit}
              />
            </div>

            <div class="form-group">
              <label for="description">Description*</label>
              <input
                type="text"
                id="description"
                name="description"
                required
                bind:value={formData.description}
              />
            </div>

            <div class="form-group">
              <label for="priceExclVAT">Price excl VAT*</label>
              <input
                type="text"
                id="priceExclVAT"
                name="priceExclVAT"
                required
                bind:value={formData.priceExclVAT}
                placeholder="e.g., 1,90"
              />
            </div>

            <div class="form-group">
              <label for="discountCode">Discount Code</label>
              <input
                type="number"
                id="discountCode"
                name="discountCode"
                min="0"
                bind:value={formData.discountCode}
              />
            </div>

            <div class="form-group">
              <label for="productGroup">Product Group</label>
              <input
                type="number"
                id="productGroup"
                name="productGroup"
                min="0"
                bind:value={formData.productGroup}
                list="productGroups"
              />
              <datalist id="productGroups">
                {#each productGroups as group}
                  <option value={group}></option>
                {/each}
              </datalist>
            </div>

            <div class="form-group">
              <label for="functionGroup">Function Group</label>
              <input
                type="number"
                id="functionGroup"
                name="functionGroup"
                min="0"
                bind:value={formData.functionGroup}
                list="functionGroups"
              />
              <datalist id="functionGroups">
                {#each functionGroups as group}
                  <option value={group}></option>
                {/each}
              </datalist>
            </div>

            <div class="form-group">
              <label for="statisticNo">Statistic No</label>
              <input
                type="number"
                id="statisticNo"
                name="statisticNo"
                min="0"
                bind:value={formData.statisticNo}
              />
            </div>

            <div class="form-group">
              <label for="weight">Weight</label>
              <input
                type="text"
                id="weight"
                name="weight"
                bind:value={formData.weight}
                placeholder="e.g., 0,034"
              />
            </div>

            <div class="form-group">
              <label for="countryOfOrigin">Country of Origin</label>
              <input
                type="text"
                id="countryOfOrigin"
                name="countryOfOrigin"
                bind:value={formData.countryOfOrigin}
                list="countries"
                maxlength="2"
                placeholder="e.g., DE"
              />
              <datalist id="countries">
                {#each countries as country}
                  <option value={country}></option>
                {/each}
              </datalist>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-secondary" on:click={resetForm}>Cancel</button>
            <button type="submit" class="btn-primary">{editingItem ? 'Update' : 'Create'}</button>
          </div>
        </form>
      </div>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .back-button:hover {
    background-color: #e2e8f0;
    color: #475569;
  }

  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
  }

  .filters {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .search-input,
  .filter-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
  }

  .search-input:focus,
  .filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .results-info {
    margin-bottom: 1rem;
    color: #64748b;
    font-size: 0.875rem;
  }

  .table-container {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .price-table {
    width: 100%;
    border-collapse: collapse;
  }

  .price-table th {
    background-color: #f8fafc;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
  }

  .price-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
  }

  .price-table th.sortable:hover {
    background-color: #f1f5f9;
  }

  .sort-indicator {
    margin-left: 0.5rem;
    color: #3b82f6;
  }

  .price-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .table-row:hover {
    background-color: #f8fafc;
  }

  .description-cell {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .status-badge.active {
    background-color: #dcfce7;
    color: #166534;
  }

  .status-badge.inactive {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .actions-cell {
    display: flex;
    gap: 0.5rem;
  }

  .btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .btn-icon.view:hover {
    background-color: #eff6ff;
    color: #2563eb;
    border-color: #bfdbfe;
  }

  .btn-icon.edit:hover {
    background-color: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
  }

  .btn-icon.delete:hover {
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
  }

  .pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: #374151;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #f8fafc;
  }

  .pagination-btn.active {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Button styles */
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid transparent;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    background-color: #f8fafc;
    color: #64748b;
    border-color: #e2e8f0;
  }

  .btn-secondary:hover {
    background-color: #e2e8f0;
    color: #475569;
  }

  .btn-danger {
    background-color: #dc2626;
    color: white;
    border-color: #dc2626;
  }

  .btn-danger:hover {
    background-color: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Modal styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    backdrop-filter: blur(4px);
  }

  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 42rem;
    max-height: 90vh;
    overflow-y: auto;
    padding: 1.5rem;
    animation: modal-appear 0.3s ease-out;
  }

  @keyframes modal-appear {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group.checkbox-group {
    grid-column: span 2;
    flex-direction: row;
    align-items: center;
  }

  .form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0 !important;
  }

  .form-group input,
  .form-group select {
    padding: 0.625rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .filters {
      grid-template-columns: 1fr;
    }

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .form-group.checkbox-group {
      grid-column: span 1;
    }

    .modal-content {
      margin: 1rem;
      max-width: calc(100% - 2rem);
    }
  }
</style>
